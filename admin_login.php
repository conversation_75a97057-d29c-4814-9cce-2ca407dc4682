<?php include 'includes/header.php'; ?>

<div class="form-container">
  <h2>🔐 Admin Login</h2>
  <p style="text-align: center; margin-bottom: 2rem; color: #666;">
    Administrative access to MediCarePlus system
  </p>

  <?php if (isset($_GET['logout']) && $_GET['logout'] == '1'): ?>
    <div class="success">
      🎉 You have been successfully logged out.
    </div>
  <?php endif; ?>

  <form method="post" action="">
    <div style="position: relative;">
      <input name="username" placeholder="Admin Username" required style="padding-left: 40px;">
      <span style="position: absolute; left: 12px; top: 12px; color: #666;">👤</span>
    </div>

    <div style="position: relative;">
      <input name="password" type="password" placeholder="Admin Password" required style="padding-left: 40px;">
      <span style="position: absolute; left: 12px; top: 12px; color: #666;">🔒</span>
    </div>

    <button type="submit" class="btn-primary">🚪 Admin Sign In</button>
  </form>

  <p style="text-align: center; margin-top: 2rem;">
    <a href="index.php" style="color: #2c5aa0; text-decoration: none;">← Back to Home</a> |
    <a href="doctor_login.php" style="color: #2c5aa0; text-decoration: none;">Doctor Login</a> |
    <a href="login.php" style="color: #2c5aa0; text-decoration: none;">Patient Login</a>
  </p>

  <div style="background: #e7f3ff; padding: 1rem; border-radius: 5px; margin-top: 1rem; text-align: center;">
    <small style="color: #0066cc;">
      <strong>Default Credentials:</strong> admin / admin123<br>
      <a href="fix_admin_password.php" style="color: #0066cc;">🔧 Fix Password Issues</a> |
      <a href="check_admin_setup.php" style="color: #0066cc;">Check Setup</a> |
      <a href="migrate_to_new_schema.php" style="color: #0066cc;">Run Migration</a>
    </small>
  </div>
</div>

<?php
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
  // Admin login authentication
  $stmt = $conn->prepare(
    "SELECT admin_id, password_hash, full_name, email, phone, role
     FROM admins
     WHERE username = ?"
  );

  if ($stmt === false) {
    echo "<p class='error'>Database error: " . $conn->error . "</p>";
    echo "<p class='error'>Please make sure the admins table exists. <a href='check_admin_setup.php'>Check admin setup</a></p>";
  } else {
    $stmt->bind_param('s', $_POST['username']);
    $stmt->execute();
    $stmt->bind_result($admin_id, $password_hash, $full_name, $email, $phone, $role);

    if ($stmt->fetch()) {
      if (password_verify($_POST['password'], $password_hash)) {
        // Update last login time
        $stmt->close();
        $update_login = $conn->prepare("UPDATE admins SET last_login = NOW() WHERE admin_id = ?");
        $update_login->bind_param('i', $admin_id);
        $update_login->execute();

        // Set session variables
        $_SESSION = [
          'user_type' => 'admin',
          'user_id' => $admin_id,
          'admin_name' => $full_name,
          'admin_email' => $email,
          'admin_phone' => $phone,
          'admin_role' => $role
        ];

        header("Location: admin_dashboard.php");
        exit;
      } else {
        $stmt->close();
        echo "<p class='error'>Invalid password for user '" . htmlspecialchars($_POST['username']) . "'.</p>";
        echo "<p class='error'><a href='check_admin_setup.php'>Reset password here</a></p>";
      }
    } else {
      $stmt->close();
      echo "<p class='error'>Username '" . htmlspecialchars($_POST['username']) . "' not found.</p>";
      echo "<p class='error'><a href='check_admin_setup.php'>Check admin setup</a></p>";
    }
  }
}
?>

<?php include 'includes/footer.php'; ?>
