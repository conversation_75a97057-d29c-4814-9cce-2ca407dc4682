<?php
// Quick fix for admin password issues
require_once 'includes/data.php';

echo "<h2>🔧 Admin Password Fix</h2>";

// Check if admins table exists
$table_check = $conn->query("SHOW TABLES LIKE 'admins'");
if ($table_check->num_rows == 0) {
    echo "<p style='color: red;'>❌ Admins table does not exist. Please run the database migration first.</p>";
    echo "<p><a href='migrate_to_new_schema.php'>Run Database Migration</a></p>";
    exit;
}

// Handle password reset
if (isset($_POST['reset_admin_password'])) {
    $username = 'admin';
    $new_password = 'admin123';
    $password_hash = password_hash($new_password, PASSWORD_BCRYPT);
    
    // First, check if admin user exists
    $check_stmt = $conn->prepare("SELECT admin_id FROM admins WHERE username = ?");
    $check_stmt->bind_param('s', $username);
    $check_stmt->execute();
    $result = $check_stmt->get_result();
    
    if ($result->num_rows > 0) {
        // Update existing admin password
        $update_stmt = $conn->prepare("UPDATE admins SET password_hash = ? WHERE username = ?");
        $update_stmt->bind_param('ss', $password_hash, $username);
        
        if ($update_stmt->execute()) {
            echo "<p style='color: green;'>✅ Admin password reset successfully!</p>";
            echo "<p><strong>Username:</strong> admin</p>";
            echo "<p><strong>Password:</strong> admin123</p>";
        } else {
            echo "<p style='color: red;'>❌ Error updating password: " . $update_stmt->error . "</p>";
        }
    } else {
        // Create new admin user
        $create_stmt = $conn->prepare("INSERT INTO admins (full_name, email, phone, username, password_hash, role) VALUES (?, ?, ?, ?, ?, ?)");
        $full_name = 'System Administrator';
        $email = '<EMAIL>';
        $phone = '+***********';
        $role = 'super_admin';
        
        $create_stmt->bind_param('ssssss', $full_name, $email, $phone, $username, $password_hash, $role);
        
        if ($create_stmt->execute()) {
            echo "<p style='color: green;'>✅ Admin account created successfully!</p>";
            echo "<p><strong>Username:</strong> admin</p>";
            echo "<p><strong>Password:</strong> admin123</p>";
        } else {
            echo "<p style='color: red;'>❌ Error creating admin: " . $create_stmt->error . "</p>";
        }
    }
}

// Handle custom password reset
if (isset($_POST['set_custom_password'])) {
    $username = $_POST['username'];
    $new_password = $_POST['new_password'];
    $password_hash = password_hash($new_password, PASSWORD_BCRYPT);
    
    $update_stmt = $conn->prepare("UPDATE admins SET password_hash = ? WHERE username = ?");
    $update_stmt->bind_param('ss', $password_hash, $username);
    
    if ($update_stmt->execute() && $update_stmt->affected_rows > 0) {
        echo "<p style='color: green;'>✅ Password updated for user: " . htmlspecialchars($username) . "</p>";
        echo "<p><strong>New Password:</strong> " . htmlspecialchars($new_password) . "</p>";
    } else {
        echo "<p style='color: red;'>❌ Error updating password or user not found.</p>";
    }
}

// Test login function
if (isset($_POST['test_login'])) {
    $test_username = $_POST['test_username'];
    $test_password = $_POST['test_password'];
    
    echo "<h3>🧪 Testing Login...</h3>";
    
    $stmt = $conn->prepare("SELECT admin_id, password_hash, full_name FROM admins WHERE username = ?");
    $stmt->bind_param('s', $test_username);
    $stmt->execute();
    $stmt->bind_result($admin_id, $stored_hash, $full_name);
    
    if ($stmt->fetch()) {
        echo "<p style='color: blue;'>ℹ️ User found: " . htmlspecialchars($full_name) . "</p>";
        echo "<p style='color: blue;'>ℹ️ Stored hash: " . substr($stored_hash, 0, 20) . "...</p>";
        
        if (password_verify($test_password, $stored_hash)) {
            echo "<p style='color: green;'>✅ Password verification successful!</p>";
            echo "<p style='color: green;'>✅ Login should work now.</p>";
        } else {
            echo "<p style='color: red;'>❌ Password verification failed.</p>";
            echo "<p style='color: orange;'>⚠️ The stored password hash doesn't match the provided password.</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ User not found in database.</p>";
    }
    $stmt->close();
}

// Show current admin accounts
echo "<h3>📋 Current Admin Accounts</h3>";
$admins = $conn->query("SELECT admin_id, username, full_name, email, role, created_at FROM admins ORDER BY admin_id");

if ($admins && $admins->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 1rem 0;'>";
    echo "<tr style='background: #f0f0f0;'><th>ID</th><th>Username</th><th>Full Name</th><th>Email</th><th>Role</th><th>Created</th></tr>";
    while ($admin = $admins->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $admin['admin_id'] . "</td>";
        echo "<td><strong>" . htmlspecialchars($admin['username']) . "</strong></td>";
        echo "<td>" . htmlspecialchars($admin['full_name']) . "</td>";
        echo "<td>" . htmlspecialchars($admin['email']) . "</td>";
        echo "<td>" . htmlspecialchars($admin['role']) . "</td>";
        echo "<td>" . date('M j, Y', strtotime($admin['created_at'])) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: orange;'>⚠️ No admin accounts found.</p>";
}
?>

<div style="background: #f8f9fa; padding: 2rem; border-radius: 10px; margin: 2rem 0;">
    <h3>🔄 Quick Fix: Reset Default Admin Password</h3>
    <p>This will reset the 'admin' user password to 'admin123'</p>
    <form method="post">
        <input type="hidden" name="reset_admin_password" value="1">
        <button type="submit" style="background: #28a745; color: white; padding: 1rem 2rem; border: none; border-radius: 5px; cursor: pointer; font-size: 1rem;">
            🔧 Reset Admin Password
        </button>
    </form>
</div>

<div style="background: #fff3cd; padding: 2rem; border-radius: 10px; margin: 2rem 0;">
    <h3>🧪 Test Login Credentials</h3>
    <form method="post">
        <input type="hidden" name="test_login" value="1">
        <p>
            <label><strong>Username:</strong></label><br>
            <input type="text" name="test_username" value="admin" required style="padding: 0.5rem; width: 200px;">
        </p>
        <p>
            <label><strong>Password:</strong></label><br>
            <input type="password" name="test_password" value="admin123" required style="padding: 0.5rem; width: 200px;">
        </p>
        <button type="submit" style="background: #007bff; color: white; padding: 0.75rem 1.5rem; border: none; border-radius: 5px; cursor: pointer;">
            🧪 Test Login
        </button>
    </form>
</div>

<div style="background: #e7f3ff; padding: 2rem; border-radius: 10px; margin: 2rem 0;">
    <h3>🔑 Set Custom Password</h3>
    <form method="post">
        <input type="hidden" name="set_custom_password" value="1">
        <p>
            <label><strong>Username:</strong></label><br>
            <input type="text" name="username" value="admin" required style="padding: 0.5rem; width: 200px;">
        </p>
        <p>
            <label><strong>New Password:</strong></label><br>
            <input type="password" name="new_password" value="admin123" required style="padding: 0.5rem; width: 200px;">
        </p>
        <button type="submit" style="background: #ffc107; color: black; padding: 0.75rem 1.5rem; border: none; border-radius: 5px; cursor: pointer;">
            🔑 Set Password
        </button>
    </form>
</div>

<div style="text-align: center; margin: 2rem 0;">
    <a href="admin_login.php" style="background: #6c757d; color: white; padding: 1rem 2rem; text-decoration: none; border-radius: 5px; margin-right: 1rem;">
        ← Back to Admin Login
    </a>
    <a href="check_admin_setup.php" style="background: #17a2b8; color: white; padding: 1rem 2rem; text-decoration: none; border-radius: 5px;">
        Check Admin Setup
    </a>
</div>

<div style="background: #d4edda; padding: 1rem; border-radius: 5px; margin: 2rem 0;">
    <h4>💡 Troubleshooting Tips:</h4>
    <ul>
        <li><strong>Password Hash Issue:</strong> Use the "Reset Admin Password" button above</li>
        <li><strong>User Not Found:</strong> The reset will create a new admin user if needed</li>
        <li><strong>Database Issues:</strong> Make sure you've run the database migration</li>
        <li><strong>Still Having Issues:</strong> Check your database connection in includes/data.php</li>
    </ul>
</div>
